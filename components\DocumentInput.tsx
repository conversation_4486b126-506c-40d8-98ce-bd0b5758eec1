import React, { useRef, useState } from 'react';
import { documentProcessor } from '../services/documentProcessor';

interface DocumentInputProps {
  onUpload: (file: File) => void;
}

export const DocumentInput: React.FC<DocumentInputProps> = ({ onUpload }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  const handleFileSelect = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0];
    setIsValidating(true);

    try {
      // Validar arquivo
      const validation = documentProcessor.validateFile(file);
      if (!validation.valid) {
        alert(validation.error);
        return;
      }

      // Estimar tempo de processamento
      const estimatedTime = documentProcessor.estimateProcessingTime(file);
      const timeInSeconds = Math.ceil(estimatedTime / 1000);

      const confirmed = confirm(
        `Arquivo: ${file.name}\n` +
        `Tamanho: ${(file.size / 1024 / 1024).toFixed(2)} MB\n` +
        `Tempo estimado: ~${timeInSeconds}s\n\n` +
        `Deseja continuar com o upload?`
      );

      if (confirmed) {
        onUpload(file);
      }
    } catch (error) {
      console.error('Erro na validação:', error);
      alert('Erro ao validar arquivo. Tente novamente.');
    } finally {
      setIsValidating(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="w-full">
      <div
        className={`
          glass-card border-2 border-dashed rounded-xl p-6 text-center cursor-pointer transition-all duration-300
          ${isDragging
            ? 'border-orange-500 bg-orange-50/50'
            : 'border-gray-400 hover:border-orange-400 hover:bg-white/30'
          }
          ${isValidating ? 'opacity-50 cursor-not-allowed' : ''}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={!isValidating ? handleClick : undefined}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept=".pdf,.txt,.md,.doc,.docx"
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
          disabled={isValidating}
        />

        <div className="flex flex-col items-center space-y-3">
          {isValidating ? (
            <>
              <div className="loading-modern">
                <div className="loading-dot"></div>
                <div className="loading-dot"></div>
                <div className="loading-dot"></div>
              </div>
              <p className="text-gray-700 font-medium">Validando arquivo...</p>
            </>
          ) : (
            <>
              <div className="w-12 h-12 bg-gradient-to-r from-gray-500 to-orange-500 rounded-full flex items-center justify-center mb-2">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              </div>

              <div>
                <p className="text-lg font-medium text-gray-800">
                  {isDragging ? '📄 Solte o arquivo aqui' : '📄 Carregar documento'}
                </p>
                <p className="text-sm text-gray-600 mt-1">
                  Arraste e solte ou clique para selecionar
                </p>
              </div>

              <div className="text-xs text-gray-500 space-y-1">
                <p>• PDF, TXT, MD, DOC, DOCX</p>
                <p>• Tamanho máximo: 10MB</p>
                <p>• O documento será processado automaticamente</p>
              </div>
            </>
          )}
        </div>
      </div>

      <div className="mt-3 text-xs text-gray-500">
        <p className="font-medium mb-1">💡 Dica:</p>
        <p>
          Carregue documentos oficiais da Câmara Municipal, projetos de lei, 
          regulamentos ou outros materiais relevantes para melhorar as respostas da IA.
        </p>
      </div>
    </div>
  );
};
