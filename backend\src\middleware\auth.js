import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { Logger } from '../utils/Logger.js';

const logger = new Logger();

// Chaves de API válidas (em produção, usar banco de dados)
const API_KEYS = new Map([
  ['frontend', {
    key: process.env.FRONTEND_API_KEY || 'frontend-key-' + crypto.randomBytes(16).toString('hex'),
    permissions: ['whatsapp:read', 'whatsapp:write', 'session:read'],
    name: 'Frontend Application',
    lastUsed: null,
    createdAt: new Date()
  }],
  ['admin', {
    key: process.env.ADMIN_API_KEY || 'admin-key-' + crypto.randomBytes(16).toString('hex'),
    permissions: ['*'],
    name: 'Admin Access',
    lastUsed: null,
    createdAt: new Date()
  }]
]);

// Middleware de autenticação por API Key
export const authenticateApiKey = (requiredPermissions = []) => {
  return (req, res, next) => {
    try {
      const apiKey = req.headers['x-api-key'] || req.query.apiKey;
      
      if (!apiKey) {
        logger.security('API key missing', { 
          ip: req.ip, 
          userAgent: req.get('User-Agent'),
          endpoint: req.path 
        });
        
        return res.status(401).json({
          error: 'API key required',
          message: 'Forneça uma API key válida no header X-API-Key',
          code: 'API_KEY_REQUIRED'
        });
      }

      // Verificar se a API key existe
      let keyData = null;
      let keyName = null;
      
      for (const [name, data] of API_KEYS.entries()) {
        if (data.key === apiKey) {
          keyData = data;
          keyName = name;
          break;
        }
      }

      if (!keyData) {
        logger.security('Invalid API key used', { 
          ip: req.ip, 
          userAgent: req.get('User-Agent'),
          endpoint: req.path,
          apiKey: apiKey.substring(0, 10) + '...'
        });
        
        return res.status(401).json({
          error: 'Invalid API key',
          message: 'A API key fornecida não é válida',
          code: 'INVALID_API_KEY'
        });
      }

      // Verificar permissões
      if (requiredPermissions.length > 0) {
        const hasPermission = requiredPermissions.some(permission => 
          keyData.permissions.includes('*') || 
          keyData.permissions.includes(permission)
        );

        if (!hasPermission) {
          logger.security('Insufficient permissions', { 
            ip: req.ip, 
            keyName,
            requiredPermissions,
            userPermissions: keyData.permissions,
            endpoint: req.path
          });
          
          return res.status(403).json({
            error: 'Insufficient permissions',
            message: 'Sua API key não tem permissões suficientes',
            code: 'INSUFFICIENT_PERMISSIONS',
            required: requiredPermissions
          });
        }
      }

      // Atualizar último uso
      keyData.lastUsed = new Date();
      
      // Adicionar informações ao request
      req.auth = {
        keyName,
        permissions: keyData.permissions,
        isAdmin: keyData.permissions.includes('*')
      };

      logger.info(`API access granted: ${keyName}`, {
        endpoint: req.path,
        method: req.method,
        ip: req.ip
      });

      next();
    } catch (error) {
      logger.error('Authentication error:', error);
      res.status(500).json({
        error: 'Authentication error',
        message: 'Erro interno de autenticação',
        code: 'AUTH_ERROR'
      });
    }
  };
};

// Middleware de autenticação JWT (para sessões web)
export const authenticateJWT = (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        error: 'Token required',
        message: 'Token JWT é obrigatório',
        code: 'TOKEN_REQUIRED'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default-secret');
    req.user = decoded;
    
    logger.info(`JWT access granted: ${decoded.sub}`, {
      endpoint: req.path,
      method: req.method,
      ip: req.ip
    });
    
    next();
  } catch (error) {
    logger.security('Invalid JWT token', { 
      ip: req.ip, 
      endpoint: req.path,
      error: error.message 
    });
    
    res.status(401).json({
      error: 'Invalid token',
      message: 'Token JWT inválido ou expirado',
      code: 'INVALID_TOKEN'
    });
  }
};

// Middleware para verificar origem (CORS avançado)
export const verifyOrigin = (req, res, next) => {
  const allowedOrigins = [
    process.env.FRONTEND_URL || 'http://localhost:3000',
    'http://localhost:3000',
    'http://localhost:3001',
    'http://127.0.0.1:3000',
    'http://127.0.0.1:3001'
  ];

  const origin = req.headers.origin;
  const referer = req.headers.referer;

  // Permitir requisições sem origin (Postman, curl, etc.) apenas em desenvolvimento
  if (!origin && !referer && process.env.NODE_ENV === 'development') {
    return next();
  }

  if (origin && allowedOrigins.includes(origin)) {
    return next();
  }

  if (referer && allowedOrigins.some(allowed => referer.startsWith(allowed))) {
    return next();
  }

  logger.security('Blocked request from unauthorized origin', {
    origin,
    referer,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.status(403).json({
    error: 'Forbidden origin',
    message: 'Origem não autorizada',
    code: 'FORBIDDEN_ORIGIN'
  });
};

// Middleware de rate limiting por IP
const rateLimitMap = new Map();

export const rateLimitByIP = (maxRequests = 100, windowMs = 60000) => {
  return (req, res, next) => {
    const ip = req.ip;
    const now = Date.now();
    
    if (!rateLimitMap.has(ip)) {
      rateLimitMap.set(ip, { count: 1, resetTime: now + windowMs });
      return next();
    }

    const ipData = rateLimitMap.get(ip);
    
    if (now > ipData.resetTime) {
      // Reset window
      ipData.count = 1;
      ipData.resetTime = now + windowMs;
      return next();
    }

    if (ipData.count >= maxRequests) {
      logger.security('Rate limit exceeded', { 
        ip, 
        count: ipData.count, 
        endpoint: req.path 
      });
      
      return res.status(429).json({
        error: 'Rate limit exceeded',
        message: 'Muitas requisições. Tente novamente em alguns minutos.',
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil((ipData.resetTime - now) / 1000)
      });
    }

    ipData.count++;
    next();
  };
};

// Função para gerar nova API key
export const generateApiKey = (name, permissions = []) => {
  const key = 'ak_' + crypto.randomBytes(32).toString('hex');
  
  API_KEYS.set(name, {
    key,
    permissions,
    name,
    lastUsed: null,
    createdAt: new Date()
  });

  logger.info(`New API key generated: ${name}`, { permissions });
  
  return key;
};

// Função para revogar API key
export const revokeApiKey = (name) => {
  const deleted = API_KEYS.delete(name);
  
  if (deleted) {
    logger.info(`API key revoked: ${name}`);
  }
  
  return deleted;
};

// Função para listar API keys (sem expor as chaves)
export const listApiKeys = () => {
  const keys = [];
  
  for (const [name, data] of API_KEYS.entries()) {
    keys.push({
      name: data.name,
      permissions: data.permissions,
      lastUsed: data.lastUsed,
      createdAt: data.createdAt,
      keyPrefix: data.key.substring(0, 10) + '...'
    });
  }
  
  return keys;
};

// Middleware para logs de auditoria
export const auditLog = (req, res, next) => {
  const startTime = Date.now();
  
  // Override res.json para capturar resposta
  const originalJson = res.json;
  res.json = function(data) {
    const duration = Date.now() - startTime;
    
    logger.info('API Request completed', {
      method: req.method,
      endpoint: req.path,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      auth: req.auth?.keyName || 'unauthenticated',
      responseSize: JSON.stringify(data).length
    });
    
    return originalJson.call(this, data);
  };
  
  next();
};
