import { createLogger } from '../utils/logger.js';

class RafaelaResponseService {
  constructor() {
    this.logger = createLogger('RafaelaResponseService');
    
    // Emojis favoritos da Vereadora Rafaela
    this.emojis = {
      prayer: '🙏🏽',
      heart: '💖',
      excited: '🤩',
      love: '😍',
      clap: '👏'
    };

    // Padrões de resposta da Vereadora Rafaela
    this.responsePatterns = this.initializePatterns();
  }

  initializePatterns() {
    return {
      // Saudações sem número salvo
      unknownGreeting: {
        patterns: [
          /^(bom dia|boa tarde|boa noite|oi|olá|oie)/i,
        ],
        response: `Oi! Como posso te ajudar? ${this.emojis.prayer}${this.emojis.heart}`
      },

      // Pedidos de cesta básica
      cestaBasica: {
        patterns: [
          /cesta\s*básica/i,
          /cesta\s*de\s*alimento/i,
          /ajuda\s*alimentar/i,
          /alimento/i,
          /comida/i,
          /fome/i
        ],
        response: `Já tem o cadastro no CRAS? ${this.emojis.prayer}`
      },

      // Medicação
      medicacao: {
        patterns: [
          /medicação/i,
          /medicamento/i,
          /remédio/i,
          /farmácia/i,
          /receita/i,
          /tratamento/i
        ],
        response: `Já tentou ver na farmácia da UBS que você é atendida? ${this.emojis.prayer}`
      },

      // Pedido de emprego
      emprego: {
        patterns: [
          /emprego/i,
          /trabalho/i,
          /vaga/i,
          /contratação/i,
          /oportunidade/i,
          /concurso/i
        ],
        response: `Vou ser bem transparente com você, a situação financeira da prefeitura está bastante delicada no momento, e infelizmente não há como atender a todas as solicitações de emprego que recebo. Meu compromisso como vereadora é lutar para que mais oportunidades sejam geradas em nossa cidade, e continuo trabalhando para que isso aconteça. ${this.emojis.prayer}${this.emojis.heart}`
      },

      // Cirurgias e exames
      cirurgiaExame: {
        patterns: [
          /cirurgia/i,
          /exame/i,
          /consulta/i,
          /médico/i,
          /hospital/i,
          /especialista/i,
          /regulação/i
        ],
        response: `Já deu entrada na central de regulação? ${this.emojis.prayer}`
      },

      // Canal dentário
      canalDentario: {
        patterns: [
          /canal/i,
          /dentista/i,
          /dente/i,
          /dental/i,
          /odonto/i
        ],
        response: `Já pegou o encaminhamento com o dentista da UBS que você é atendida? ${this.emojis.prayer}`
      },

      // Mensagens de bom dia e de Deus
      bomDiaDeus: {
        patterns: [
          /bom\s*dia.*deus/i,
          /deus.*bom\s*dia/i,
          /glória.*deus/i,
          /deus.*abençoe/i,
          /amém/i,
          /aleluia/i,
          /graças.*deus/i
        ],
        response: `Bom diaaa, amém! ${this.emojis.prayer}${this.emojis.heart}`
      },

      // Agenda
      agenda: {
        patterns: [
          /agenda/i,
          /reunião/i,
          /encontro/i,
          /conversar/i,
          /marcar/i,
          /horário/i,
          /atendimento/i
        ],
        response: `Qual seria a pauta? Vou verificar com a pessoa que cuida da minha agenda o melhor dia e horário e te passo em seguida! ${this.emojis.prayer}${this.emojis.heart}`
      },

      // Vereadora preciso falar contigo
      precisoFalar: {
        patterns: [
          /preciso\s*falar/i,
          /quero\s*falar/i,
          /conversar.*você/i,
          /falar.*contigo/i,
          /falar.*com\s*você/i
        ],
        response: `Oii, pode ser por aqui mesmo ou só pessoalmente? Estou à disposição! ${this.emojis.prayer}${this.emojis.heart}`
      },

      // Mensagens de gratidão
      gratidao: {
        patterns: [
          /obrigad[oa]/i,
          /valeu/i,
          /agradeço/i,
          /muito\s*grat[oa]/i
        ],
        response: `De nada! Estou sempre à disposição! ${this.emojis.prayer}${this.emojis.heart}`
      },

      // Elogios
      elogios: {
        patterns: [
          /parabéns/i,
          /excelente/i,
          /ótim[oa]/i,
          /maravilhos[oa]/i,
          /incrível/i
        ],
        response: `Muito obrigada! ${this.emojis.excited}${this.emojis.heart} Isso me motiva ainda mais a trabalhar por nossa cidade! ${this.emojis.clap}`
      }
    };
  }

  // Analisar mensagem e gerar resposta
  generateResponse(message, contact = null) {
    try {
      this.logger.info(`🤖 Analisando mensagem: "${message}"`);
      
      const cleanMessage = message.trim().toLowerCase();
      
      // Verificar se é um contato conhecido
      const isKnownContact = contact && contact.name && contact.name !== 'Sem nome';
      
      // Verificar padrões em ordem de prioridade
      for (const [patternName, config] of Object.entries(this.responsePatterns)) {
        for (const pattern of config.patterns) {
          if (pattern.test(cleanMessage)) {
            this.logger.info(`✅ Padrão encontrado: ${patternName}`);
            
            // Para saudações, verificar se é contato conhecido
            if (patternName === 'unknownGreeting' && isKnownContact) {
              return `Oiii, ${contact.name}! Como você está? ${this.emojis.prayer}${this.emojis.heart}`;
            }
            
            return config.response;
          }
        }
      }

      // Resposta padrão se nenhum padrão for encontrado
      this.logger.info('ℹ️ Nenhum padrão específico encontrado, usando resposta padrão');
      
      if (isKnownContact) {
        return `Oi, ${contact.name}! Como posso te ajudar hoje? ${this.emojis.prayer}${this.emojis.heart}`;
      } else {
        return `Olá! Como posso te ajudar? Se puder me dizer seu nome e bairro, fica mais fácil para eu te atender melhor! ${this.emojis.prayer}${this.emojis.heart}`;
      }

    } catch (error) {
      this.logger.error('❌ Erro ao gerar resposta:', error);
      return `Oi! Desculpe, tive um probleminha aqui. Pode repetir sua mensagem? ${this.emojis.prayer}`;
    }
  }

  // Verificar se deve responder automaticamente
  shouldAutoRespond(message, contact = null) {
    try {
      // Não responder para mensagens muito curtas (menos de 3 caracteres)
      if (message.trim().length < 3) {
        return false;
      }

      // Não responder para mensagens que parecem ser respostas automáticas
      const autoResponsePatterns = [
        /esta\s*mensagem\s*foi\s*enviada\s*automaticamente/i,
        /auto\s*resposta/i,
        /mensagem\s*automática/i
      ];

      for (const pattern of autoResponsePatterns) {
        if (pattern.test(message)) {
          return false;
        }
      }

      // Não responder para mídias sem texto
      if (message.trim() === '' || message === '[Mídia]') {
        return false;
      }

      return true;

    } catch (error) {
      this.logger.error('❌ Erro ao verificar auto-resposta:', error);
      return false;
    }
  }

  // Adicionar contexto personalizado à resposta
  addPersonalizedContext(response, contact = null, additionalInfo = {}) {
    try {
      let personalizedResponse = response;

      // Adicionar nome se disponível
      if (contact && contact.name && contact.name !== 'Sem nome') {
        // Se a resposta não contém o nome, adicionar no início
        if (!personalizedResponse.includes(contact.name)) {
          personalizedResponse = `${contact.name}, ${personalizedResponse.toLowerCase()}`;
        }
      }

      // Adicionar informações de horário se relevante
      const now = new Date();
      const hour = now.getHours();
      
      if (hour < 12 && !personalizedResponse.includes('bom dia')) {
        // Manhã
      } else if (hour < 18 && !personalizedResponse.includes('boa tarde')) {
        // Tarde
      } else if (!personalizedResponse.includes('boa noite')) {
        // Noite
      }

      return personalizedResponse;

    } catch (error) {
      this.logger.error('❌ Erro ao personalizar resposta:', error);
      return response;
    }
  }

  // Obter estatísticas de uso
  getStats() {
    return {
      totalPatterns: Object.keys(this.responsePatterns).length,
      availableEmojis: Object.keys(this.emojis).length,
      lastUpdate: new Date().toISOString()
    };
  }
}

export default RafaelaResponseService;
