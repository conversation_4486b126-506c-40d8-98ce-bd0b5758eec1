import express from 'express';
import crypto from 'crypto';
import { Logger } from '../utils/Logger.js';

const router = express.Router();
const logger = new Logger();

// Middleware para verificar assinatura do webhook (se configurado)
const verifyWebhookSignature = (req, res, next) => {
  const webhookSecret = process.env.WEBHOOK_SECRET;
  
  if (!webhookSecret) {
    // Se não há secret configurado, pular verificação
    return next();
  }
  
  const signature = req.headers['x-webhook-signature'];
  if (!signature) {
    return res.status(401).json({
      error: 'Assinatura ausente',
      message: 'Header x-webhook-signature é obrigatório',
      code: 'SIGNATURE_MISSING'
    });
  }
  
  const body = JSON.stringify(req.body);
  const expectedSignature = crypto
    .createHmac('sha256', webhookSecret)
    .update(body)
    .digest('hex');
  
  if (signature !== `sha256=${expectedSignature}`) {
    logger.security('Webhook signature mismatch', { 
      received: signature,
      expected: `sha256=${expectedSignature}`
    });
    
    return res.status(401).json({
      error: 'Assinatura inválida',
      message: 'A assinatura do webhook não confere',
      code: 'SIGNATURE_INVALID'
    });
  }
  
  next();
};

// POST /api/webhook/whatsapp - Webhook para eventos do WhatsApp
router.post('/whatsapp', verifyWebhookSignature, async (req, res) => {
  try {
    const { event, data } = req.body;
    
    logger.info(`Webhook WhatsApp recebido: ${event}`, { event, data });
    
    // Processar diferentes tipos de eventos
    switch (event) {
      case 'message':
        await handleMessageWebhook(data);
        break;
      case 'status_change':
        await handleStatusChangeWebhook(data);
        break;
      case 'qr_code':
        await handleQRCodeWebhook(data);
        break;
      case 'connection':
        await handleConnectionWebhook(data);
        break;
      default:
        logger.warn(`Evento de webhook desconhecido: ${event}`);
    }
    
    res.json({
      success: true,
      message: 'Webhook processado com sucesso',
      event,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Erro ao processar webhook WhatsApp:', error);
    res.status(500).json({
      error: 'Erro ao processar webhook',
      message: error.message,
      code: 'WEBHOOK_ERROR'
    });
  }
});

// POST /api/webhook/rag - Webhook para eventos do sistema RAG
router.post('/rag', verifyWebhookSignature, async (req, res) => {
  try {
    const { event, data } = req.body;
    
    logger.info(`Webhook RAG recebido: ${event}`, { event, data });
    
    // Processar diferentes tipos de eventos RAG
    switch (event) {
      case 'document_processed':
        await handleDocumentProcessedWebhook(data);
        break;
      case 'conversation_started':
        await handleConversationStartedWebhook(data);
        break;
      case 'response_generated':
        await handleResponseGeneratedWebhook(data);
        break;
      default:
        logger.warn(`Evento RAG desconhecido: ${event}`);
    }
    
    res.json({
      success: true,
      message: 'Webhook RAG processado com sucesso',
      event,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Erro ao processar webhook RAG:', error);
    res.status(500).json({
      error: 'Erro ao processar webhook',
      message: error.message,
      code: 'WEBHOOK_ERROR'
    });
  }
});

// GET /api/webhook/test - Endpoint para testar webhooks
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'Webhook endpoint funcionando',
    timestamp: new Date().toISOString(),
    config: {
      webhookSecretConfigured: !!process.env.WEBHOOK_SECRET,
      webhookUrlConfigured: !!process.env.WEBHOOK_URL
    }
  });
});

// POST /api/webhook/test - Testar envio de webhook
router.post('/test', async (req, res) => {
  try {
    const { event, data } = req.body;
    
    if (!event) {
      return res.status(400).json({
        error: 'Evento obrigatório',
        message: 'O campo event é obrigatório',
        code: 'VALIDATION_ERROR'
      });
    }
    
    // Simular processamento do webhook
    logger.info(`Webhook de teste: ${event}`, { event, data });
    
    // Se há URL de webhook configurada, enviar para lá também
    if (process.env.WEBHOOK_URL) {
      await sendWebhook(event, data);
    }
    
    res.json({
      success: true,
      message: 'Webhook de teste processado',
      event,
      data,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Erro no webhook de teste:', error);
    res.status(500).json({
      error: 'Erro no teste',
      message: error.message,
      code: 'TEST_ERROR'
    });
  }
});

// Handlers para diferentes tipos de eventos

async function handleMessageWebhook(data) {
  const { from, message, timestamp } = data;
  
  logger.info('Processando webhook de mensagem', {
    from,
    messageLength: message?.length || 0,
    timestamp
  });
  
  // Aqui você pode implementar lógica adicional
  // como notificações, logging especial, etc.
}

async function handleStatusChangeWebhook(data) {
  const { sessionId, status, previousStatus } = data;
  
  logger.info('Processando webhook de mudança de status', {
    sessionId,
    status,
    previousStatus
  });
  
  // Implementar lógica para mudanças de status
  // como notificações para administradores
}

async function handleQRCodeWebhook(data) {
  const { qrCode, sessionId } = data;
  
  logger.info('Processando webhook de QR Code', {
    sessionId,
    hasQRCode: !!qrCode
  });
  
  // Implementar lógica para novo QR Code
  // como envio por email para administradores
}

async function handleConnectionWebhook(data) {
  const { sessionId, connected, timestamp } = data;
  
  logger.info('Processando webhook de conexão', {
    sessionId,
    connected,
    timestamp
  });
  
  // Implementar lógica para mudanças de conexão
}

async function handleDocumentProcessedWebhook(data) {
  const { documentId, status, processingTime } = data;
  
  logger.info('Processando webhook de documento', {
    documentId,
    status,
    processingTime
  });
  
  // Implementar lógica para documentos processados
}

async function handleConversationStartedWebhook(data) {
  const { conversationId, userId, platform } = data;
  
  logger.info('Processando webhook de conversa iniciada', {
    conversationId,
    userId,
    platform
  });
  
  // Implementar lógica para novas conversas
}

async function handleResponseGeneratedWebhook(data) {
  const { conversationId, query, response, confidence } = data;
  
  logger.info('Processando webhook de resposta gerada', {
    conversationId,
    queryLength: query?.length || 0,
    responseLength: response?.length || 0,
    confidence
  });
  
  // Implementar lógica para respostas geradas
}

// Função para enviar webhook para URL externa
async function sendWebhook(event, data) {
  try {
    const webhookUrl = process.env.WEBHOOK_URL;
    if (!webhookUrl) return;
    
    const payload = {
      event,
      data,
      timestamp: new Date().toISOString(),
      source: 'vereadora-rafaela-whatsapp-backend'
    };
    
    const body = JSON.stringify(payload);
    let headers = {
      'Content-Type': 'application/json',
      'User-Agent': 'Vereadora-Rafaela-Webhook/1.0.0'
    };
    
    // Adicionar assinatura se secret estiver configurado
    if (process.env.WEBHOOK_SECRET) {
      const signature = crypto
        .createHmac('sha256', process.env.WEBHOOK_SECRET)
        .update(body)
        .digest('hex');
      
      headers['X-Webhook-Signature'] = `sha256=${signature}`;
    }
    
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers,
      body
    });
    
    if (!response.ok) {
      throw new Error(`Webhook failed: ${response.status} ${response.statusText}`);
    }
    
    logger.info('Webhook enviado com sucesso', {
      url: webhookUrl,
      event,
      status: response.status
    });
    
  } catch (error) {
    logger.error('Erro ao enviar webhook:', error);
  }
}

export default router;
