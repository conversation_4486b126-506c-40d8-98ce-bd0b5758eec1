import fs from 'fs/promises';
import path from 'path';
import crypto from 'crypto';
import { Logger } from '../utils/Logger.js';

export class PersistenceService {
  constructor() {
    this.logger = new Logger();
    this.dataPath = path.join(process.cwd(), 'data', 'persistence');
    this.sessionsPath = path.join(this.dataPath, 'sessions');
    this.tokensPath = path.join(this.dataPath, 'tokens');
    this.backupPath = path.join(this.dataPath, 'backups');
    
    // Configurações de segurança
    this.encryptionKey = this.getEncryptionKey();
    this.algorithm = 'aes-256-gcm';
    
    this.initialize();
  }

  async initialize() {
    try {
      // Criar diretórios necessários
      await fs.mkdir(this.dataPath, { recursive: true });
      await fs.mkdir(this.sessionsPath, { recursive: true });
      await fs.mkdir(this.tokensPath, { recursive: true });
      await fs.mkdir(this.backupPath, { recursive: true });
      
      this.logger.info('✅ Persistence Service inicializado');
    } catch (error) {
      this.logger.error('❌ Erro ao inicializar Persistence Service:', error);
      throw error;
    }
  }

  // Obter ou gerar chave de criptografia
  getEncryptionKey() {
    const keyPath = path.join(this.dataPath, '.encryption.key');
    
    try {
      // Tentar ler chave existente
      const existingKey = fs.readFileSync(keyPath);
      return existingKey;
    } catch (error) {
      // Gerar nova chave se não existir
      const newKey = crypto.randomBytes(32);
      
      try {
        fs.writeFileSync(keyPath, newKey);
        this.logger.info('🔑 Nova chave de criptografia gerada');
        return newKey;
      } catch (writeError) {
        this.logger.error('❌ Erro ao salvar chave de criptografia:', writeError);
        // Usar chave temporária em memória
        return crypto.randomBytes(32);
      }
    }
  }

  // Criptografar dados
  encrypt(data) {
    try {
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher(this.algorithm, this.encryptionKey);
      cipher.setAAD(Buffer.from('whatsapp-session', 'utf8'));
      
      let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const authTag = cipher.getAuthTag();
      
      return {
        encrypted,
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex')
      };
    } catch (error) {
      this.logger.error('❌ Erro ao criptografar dados:', error);
      throw error;
    }
  }

  // Descriptografar dados
  decrypt(encryptedData) {
    try {
      const { encrypted, iv, authTag } = encryptedData;
      
      const decipher = crypto.createDecipher(this.algorithm, this.encryptionKey);
      decipher.setAAD(Buffer.from('whatsapp-session', 'utf8'));
      decipher.setAuthTag(Buffer.from(authTag, 'hex'));
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return JSON.parse(decrypted);
    } catch (error) {
      this.logger.error('❌ Erro ao descriptografar dados:', error);
      throw error;
    }
  }

  // Salvar sessão WhatsApp de forma segura
  async saveWhatsAppSession(sessionName, sessionData) {
    try {
      const sessionFile = path.join(this.sessionsPath, `${sessionName}.enc`);
      
      // Adicionar metadados
      const dataToSave = {
        sessionData,
        metadata: {
          sessionName,
          savedAt: new Date().toISOString(),
          version: '1.0.0',
          checksum: this.generateChecksum(sessionData)
        }
      };

      // Criptografar dados
      const encryptedData = this.encrypt(dataToSave);
      
      // Salvar arquivo
      await fs.writeFile(sessionFile, JSON.stringify(encryptedData, null, 2));
      
      this.logger.info(`💾 Sessão WhatsApp salva: ${sessionName}`);
      return true;
    } catch (error) {
      this.logger.error(`❌ Erro ao salvar sessão ${sessionName}:`, error);
      return false;
    }
  }

  // Carregar sessão WhatsApp
  async loadWhatsAppSession(sessionName) {
    try {
      const sessionFile = path.join(this.sessionsPath, `${sessionName}.enc`);
      
      // Verificar se arquivo existe
      await fs.access(sessionFile);
      
      // Ler arquivo
      const encryptedContent = await fs.readFile(sessionFile, 'utf8');
      const encryptedData = JSON.parse(encryptedContent);
      
      // Descriptografar
      const decryptedData = this.decrypt(encryptedData);
      
      // Verificar integridade
      const expectedChecksum = this.generateChecksum(decryptedData.sessionData);
      if (decryptedData.metadata.checksum !== expectedChecksum) {
        throw new Error('Checksum inválido - dados podem estar corrompidos');
      }
      
      this.logger.info(`📂 Sessão WhatsApp carregada: ${sessionName}`);
      return decryptedData.sessionData;
    } catch (error) {
      this.logger.warn(`⚠️ Não foi possível carregar sessão ${sessionName}:`, error.message);
      return null;
    }
  }

  // Deletar sessão
  async deleteWhatsAppSession(sessionName) {
    try {
      const sessionFile = path.join(this.sessionsPath, `${sessionName}.enc`);
      await fs.unlink(sessionFile);
      
      this.logger.info(`🗑️ Sessão WhatsApp deletada: ${sessionName}`);
      return true;
    } catch (error) {
      this.logger.error(`❌ Erro ao deletar sessão ${sessionName}:`, error);
      return false;
    }
  }

  // Listar sessões disponíveis
  async listWhatsAppSessions() {
    try {
      const files = await fs.readdir(this.sessionsPath);
      const sessions = [];
      
      for (const file of files) {
        if (file.endsWith('.enc')) {
          const sessionName = file.replace('.enc', '');
          const filePath = path.join(this.sessionsPath, file);
          const stats = await fs.stat(filePath);
          
          sessions.push({
            name: sessionName,
            size: stats.size,
            modified: stats.mtime,
            created: stats.birthtime
          });
        }
      }
      
      return sessions;
    } catch (error) {
      this.logger.error('❌ Erro ao listar sessões:', error);
      return [];
    }
  }

  // Backup automático de sessões
  async createBackup() {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupDir = path.join(this.backupPath, `backup_${timestamp}`);
      
      await fs.mkdir(backupDir, { recursive: true });
      
      // Copiar todas as sessões
      const sessions = await this.listWhatsAppSessions();
      let backedUpCount = 0;
      
      for (const session of sessions) {
        try {
          const sourceFile = path.join(this.sessionsPath, `${session.name}.enc`);
          const destFile = path.join(backupDir, `${session.name}.enc`);
          
          await fs.copyFile(sourceFile, destFile);
          backedUpCount++;
        } catch (error) {
          this.logger.warn(`⚠️ Erro ao fazer backup da sessão ${session.name}:`, error);
        }
      }
      
      // Criar arquivo de metadados do backup
      const backupMetadata = {
        timestamp,
        sessionsCount: backedUpCount,
        totalSessions: sessions.length,
        version: '1.0.0',
        createdBy: 'PersistenceService'
      };
      
      await fs.writeFile(
        path.join(backupDir, 'metadata.json'),
        JSON.stringify(backupMetadata, null, 2)
      );
      
      this.logger.info(`💾 Backup criado: ${backedUpCount}/${sessions.length} sessões`);
      return { success: true, backupDir, sessionsCount: backedUpCount };
    } catch (error) {
      this.logger.error('❌ Erro ao criar backup:', error);
      return { success: false, error: error.message };
    }
  }

  // Restaurar backup
  async restoreBackup(backupDir) {
    try {
      const metadataFile = path.join(backupDir, 'metadata.json');
      const metadata = JSON.parse(await fs.readFile(metadataFile, 'utf8'));
      
      const backupFiles = await fs.readdir(backupDir);
      let restoredCount = 0;
      
      for (const file of backupFiles) {
        if (file.endsWith('.enc')) {
          try {
            const sourceFile = path.join(backupDir, file);
            const destFile = path.join(this.sessionsPath, file);
            
            await fs.copyFile(sourceFile, destFile);
            restoredCount++;
          } catch (error) {
            this.logger.warn(`⚠️ Erro ao restaurar ${file}:`, error);
          }
        }
      }
      
      this.logger.info(`🔄 Backup restaurado: ${restoredCount} sessões`);
      return { success: true, restoredCount, metadata };
    } catch (error) {
      this.logger.error('❌ Erro ao restaurar backup:', error);
      return { success: false, error: error.message };
    }
  }

  // Limpeza automática de backups antigos
  async cleanupOldBackups(daysToKeep = 30) {
    try {
      const backupDirs = await fs.readdir(this.backupPath);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      
      let cleanedCount = 0;
      
      for (const dir of backupDirs) {
        if (dir.startsWith('backup_')) {
          const dirPath = path.join(this.backupPath, dir);
          const stats = await fs.stat(dirPath);
          
          if (stats.mtime < cutoffDate) {
            await fs.rm(dirPath, { recursive: true, force: true });
            cleanedCount++;
            this.logger.info(`🧹 Backup antigo removido: ${dir}`);
          }
        }
      }
      
      if (cleanedCount > 0) {
        this.logger.info(`🧹 Limpeza concluída: ${cleanedCount} backups removidos`);
      }
      
      return cleanedCount;
    } catch (error) {
      this.logger.error('❌ Erro na limpeza de backups:', error);
      return 0;
    }
  }

  // Gerar checksum para verificação de integridade
  generateChecksum(data) {
    return crypto
      .createHash('sha256')
      .update(JSON.stringify(data))
      .digest('hex');
  }

  // Verificar integridade de todas as sessões
  async verifyIntegrity() {
    try {
      const sessions = await this.listWhatsAppSessions();
      const results = [];
      
      for (const session of sessions) {
        try {
          const sessionData = await this.loadWhatsAppSession(session.name);
          results.push({
            name: session.name,
            valid: !!sessionData,
            error: null
          });
        } catch (error) {
          results.push({
            name: session.name,
            valid: false,
            error: error.message
          });
        }
      }
      
      const validCount = results.filter(r => r.valid).length;
      this.logger.info(`🔍 Verificação de integridade: ${validCount}/${results.length} sessões válidas`);
      
      return results;
    } catch (error) {
      this.logger.error('❌ Erro na verificação de integridade:', error);
      return [];
    }
  }

  // Obter estatísticas do sistema de persistência
  async getStats() {
    try {
      const sessions = await this.listWhatsAppSessions();
      const backupDirs = await fs.readdir(this.backupPath);
      
      const totalSize = sessions.reduce((sum, session) => sum + session.size, 0);
      const backupCount = backupDirs.filter(dir => dir.startsWith('backup_')).length;
      
      return {
        sessionsCount: sessions.length,
        totalSize,
        totalSizeFormatted: this.formatBytes(totalSize),
        backupCount,
        lastBackup: backupCount > 0 ? backupDirs
          .filter(dir => dir.startsWith('backup_'))
          .sort()
          .pop() : null,
        encryptionEnabled: true,
        integrityChecks: true
      };
    } catch (error) {
      this.logger.error('❌ Erro ao obter estatísticas:', error);
      return null;
    }
  }

  // Formatar bytes para exibição
  formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }
}
