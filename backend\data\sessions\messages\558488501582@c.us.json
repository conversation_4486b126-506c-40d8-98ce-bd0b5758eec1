[{"id": "msg_1751478753460_0.49493209536615557", "body": "", "type": "e2e_notification", "timestamp": 1751472820, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751478797227_0.5483762770261527", "body": "", "type": "e2e_notification", "timestamp": 1751472820, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751478798136_0.37300349570963065", "body": "", "type": "e2e_notification", "timestamp": 1751472820, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751478855444_0.30824918950072555", "body": "", "type": "e2e_notification", "timestamp": 1751472820, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751480574044_0.6907551543791477", "body": "", "type": "e2e_notification", "timestamp": 1751472820, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751481341396_0.20201531800242423", "body": "", "type": "e2e_notification", "timestamp": 1751472820, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751481572611_0.05068645916818193", "body": "", "type": "e2e_notification", "timestamp": 1751472820, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751481865548_0.031603263610926025", "body": "", "type": "e2e_notification", "timestamp": 1751472820, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751482250367_0.9963252287878166", "body": "", "type": "e2e_notification", "timestamp": 1751472820, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751482762655_0.5248058110517619", "body": "", "type": "e2e_notification", "timestamp": 1751472820, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751483283240_0.14275998313723393", "body": "", "type": "e2e_notification", "timestamp": 1751472820, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751481572611_0.5252121729272603", "body": "<PERSON>a tarde", "type": "chat", "timestamp": 1751481499, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": false, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751481865548_0.19189549360033653", "body": "<PERSON>a tarde", "type": "chat", "timestamp": 1751481499, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": false, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751482250367_0.08491779293538015", "body": "<PERSON>a tarde", "type": "chat", "timestamp": 1751481499, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": false, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751481572611_0.12914880155977593", "body": "Oii<PERSON>, Italo Cabral! Como você está? 🙏🏽💖", "type": "chat", "timestamp": 1751481514, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751481865548_0.5322106708952039", "body": "Oii<PERSON>, Italo Cabral! Como você está? 🙏🏽💖", "type": "chat", "timestamp": 1751481514, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751482250367_0.9306951784246342", "body": "Oii<PERSON>, Italo Cabral! Como você está? 🙏🏽💖", "type": "chat", "timestamp": 1751481514, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751481572611_0.2149141507677954", "body": "Oii<PERSON>, Italo Cabral! Como você está? 🙏🏽💖", "type": "chat", "timestamp": 1751481515, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751481865548_0.4601967096419517", "body": "Oii<PERSON>, Italo Cabral! Como você está? 🙏🏽💖", "type": "chat", "timestamp": 1751481515, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751482250367_0.03148817454484765", "body": "Oii<PERSON>, Italo Cabral! Como você está? 🙏🏽💖", "type": "chat", "timestamp": 1751481515, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751481572611_0.643153989074208", "body": "❌ *Ops! Algo deu errado*\n\n<PERSON><PERSON><PERSON><PERSON>, ocorreu um erro temporário ao processar sua mensagem.\n\n*Tente novamente em alguns instantes ou:*\n📞 Ligue: (84) 99999-9999\n📧 E-mail: <EMAIL>\n\n🏛️ *Vereadora Rafaela de Nilda*\nCâmara Municipal de Parnamirim/RN", "type": "chat", "timestamp": 1751481535, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751481865548_0.7453310802811515", "body": "❌ *Ops! Algo deu errado*\n\n<PERSON><PERSON><PERSON><PERSON>, ocorreu um erro temporário ao processar sua mensagem.\n\n*Tente novamente em alguns instantes ou:*\n📞 Ligue: (84) 99999-9999\n📧 E-mail: <EMAIL>\n\n🏛️ *Vereadora Rafaela de Nilda*\nCâmara Municipal de Parnamirim/RN", "type": "chat", "timestamp": 1751481535, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751482250367_0.07849668820149103", "body": "❌ *Ops! Algo deu errado*\n\n<PERSON><PERSON><PERSON><PERSON>, ocorreu um erro temporário ao processar sua mensagem.\n\n*Tente novamente em alguns instantes ou:*\n📞 Ligue: (84) 99999-9999\n📧 E-mail: <EMAIL>\n\n🏛️ *Vereadora Rafaela de Nilda*\nCâmara Municipal de Parnamirim/RN", "type": "chat", "timestamp": 1751481535, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751481572611_0.9743325015528186", "body": "❌ *Ops! Algo deu errado*\n\n<PERSON><PERSON><PERSON><PERSON>, ocorreu um erro temporário ao processar sua mensagem.\n\n*Tente novamente em alguns instantes ou:*\n📞 Ligue: (84) 99999-9999\n📧 E-mail: <EMAIL>\n\n🏛️ *Vereadora Rafaela de Nilda*\nCâmara Municipal de Parnamirim/RN", "type": "chat", "timestamp": 1751481539, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751481865548_0.6960912315154975", "body": "❌ *Ops! Algo deu errado*\n\n<PERSON><PERSON><PERSON><PERSON>, ocorreu um erro temporário ao processar sua mensagem.\n\n*Tente novamente em alguns instantes ou:*\n📞 Ligue: (84) 99999-9999\n📧 E-mail: <EMAIL>\n\n🏛️ *Vereadora Rafaela de Nilda*\nCâmara Municipal de Parnamirim/RN", "type": "chat", "timestamp": 1751481539, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751482250367_0.5000991355949593", "body": "❌ *Ops! Algo deu errado*\n\n<PERSON><PERSON><PERSON><PERSON>, ocorreu um erro temporário ao processar sua mensagem.\n\n*Tente novamente em alguns instantes ou:*\n📞 Ligue: (84) 99999-9999\n📧 E-mail: <EMAIL>\n\n🏛️ *Vereadora Rafaela de Nilda*\nCâmara Municipal de Parnamirim/RN", "type": "chat", "timestamp": 1751481539, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751482250367_0.39242302645967486", "body": "<PERSON><PERSON> boa tarde", "type": "chat", "timestamp": 1751481913, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": false, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751482250367_0.8347028726071712", "body": "Oii<PERSON>, Italo Cabral! Como você está? 🙏🏽💖", "type": "chat", "timestamp": 1751481922, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751482250367_0.8408142807402439", "body": "❌ *Ops! Algo deu errado*\n\n<PERSON><PERSON><PERSON><PERSON>, ocorreu um erro temporário ao processar sua mensagem.\n\n*Tente novamente em alguns instantes ou:*\n📞 Ligue: (84) 99999-9999\n📧 E-mail: <EMAIL>\n\n🏛️ *Vereadora Rafaela de Nilda*\nCâmara Municipal de Parnamirim/RN", "type": "chat", "timestamp": 1751481948, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751483283240_0.6941904415960101", "body": "<PERSON>ro saber se tem emprego pra mim, vereadora", "type": "chat", "timestamp": 1751482801, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": false, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751483283240_0.7480594118770016", "body": "<PERSON><PERSON>, vou ser bem transparente com você, a situação financeira da prefeitura está bastante delicada no momento, e infelizmente não há como atender a todas as solicitações de emprego que recebo. meu compromisso como vereadora é lutar para que mais oportunidades sejam geradas em nossa cidade, e continuo trabalhando para que isso aconteça. 🙏🏽💖", "type": "chat", "timestamp": 1751482819, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}, {"id": "msg_1751483283240_0.12075758766033373", "body": "❌ *Ops! Algo deu errado*\n\n<PERSON><PERSON><PERSON><PERSON>, ocorreu um erro temporário ao processar sua mensagem.\n\n*Tente novamente em alguns instantes ou:*\n📞 Ligue: (84) 99999-9999\n📧 E-mail: <EMAIL>\n\n🏛️ *Vereadora Rafaela de Nilda*\nCâmara Municipal de Parnamirim/RN", "type": "chat", "timestamp": 1751482837, "from": "<EMAIL>", "to": "<EMAIL>", "fromMe": true, "hasMedia": false, "isForwarded": false, "isStatus": false, "deviceType": "unknown"}]