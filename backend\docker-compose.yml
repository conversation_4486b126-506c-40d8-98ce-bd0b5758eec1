version: '3.8'

services:
  whatsapp-backend:
    build: .
    container_name: vereadora-rafaela-whatsapp
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - WHATSAPP_SESSION_NAME=vereadora-rafaela
      - FRONTEND_URL=http://localhost:3000
      - RAG_API_URL=http://localhost:3000/api
      - VEREADORA_NAME=Rafaela <PERSON>
      - MUNICIPIO=Parnamirim
      - ESTADO=RN
      - GABINETE_TELEFONE=(84) 99999-9999
      - GABINETE_EMAIL=<EMAIL>
      - HORARIO_INICIO=08:00
      - HORARIO_FIM=18:00
      - DIAS_FUNCIONAMENTO=1,2,3,4,5
      - AUTO_REPLY_ENABLED=true
      - WELCOME_MESSAGE_ENABLED=true
      - BUSINESS_HOURS_ONLY=false
      - BACKUP_ENABLED=true
      - BACKUP_INTERVAL_HOURS=24
      - LOG_LEVEL=info
    volumes:
      - whatsapp_data:/app/data
      - whatsapp_logs:/app/logs
      - whatsapp_public:/app/public
    networks:
      - vereadora-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Opcional: Nginx para proxy reverso
  nginx:
    image: nginx:alpine
    container_name: vereadora-rafaela-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - whatsapp-backend
    networks:
      - vereadora-network

volumes:
  whatsapp_data:
    driver: local
  whatsapp_logs:
    driver: local
  whatsapp_public:
    driver: local

networks:
  vereadora-network:
    driver: bridge
